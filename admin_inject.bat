@echo off
chcp 65001 >nul

:: 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 需要管理员权限，正在重新启动...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

title 管理员模式 - 微信注入工具

echo ========================================
echo 管理员模式 - 微信注入工具
echo ========================================
echo.

echo ✅ 已获得管理员权限
echo.

echo 🔍 检查微信进程...
tasklist /FI "IMAGENAME eq WeChat.exe" | find "WeChat.exe"
if %errorlevel% neq 0 (
    echo ❌ 微信未运行，请先启动微信
    pause
    exit /b 1
)

echo ✅ 微信进程已找到
echo.

echo 📋 选择注入方式:
echo 1. 图形界面注入工具 (推荐)
echo 2. 命令行自动注入
echo 3. 退出
echo.

set /p choice=请选择 (1-3): 

if "%choice%"=="1" (
    echo 启动图形界面注入工具...
    echo.
    echo 📖 操作步骤:
    echo 1. 在进程列表中找到 WeChat.exe
    echo 2. 选择主进程 (内存占用较大的那个)
    echo 3. 点击"注入"按钮
    echo 4. 等待"注入成功"提示
    echo.
    
    start "" "wechat_http_tools\注入工具(图形界面版).exe"
    
    echo ⏳ 等待您完成注入操作...
    echo 注入成功后按任意键继续测试
    pause >nul
    
) else if "%choice%"=="2" (
    echo 命令行自动注入...
    
    :: 获取微信主进程PID
    for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq WeChat.exe" /FO CSV ^| find "WeChat.exe" ^| head -1') do set WECHAT_PID=%%i
    set WECHAT_PID=%WECHAT_PID:"=%
    
    echo 目标进程PID: %WECHAT_PID%
    echo.
    
    cd wechat_http_tools
    echo %WECHAT_PID% | inject_tool.exe
    cd ..
    
) else if "%choice%"=="3" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效选择
    pause
    exit /b 1
)

echo.
echo 🧪 测试注入结果...
timeout /t 3 >nul

python quick_test.py

echo.
echo 📋 如果测试成功，您可以:
echo 1. 运行完整测试: python wechat_http_api_solution.py
echo 2. 开始自动化: python miniprogram_automation_final.py
echo.

pause
