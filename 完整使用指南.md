# 微信小程序自动化完整使用指南

## 🎯 项目概述

基于WeChatPYAPI HTTP接口的微信小程序自动化解决方案，支持：
- ✅ 获取小程序code
- ✅ 发送小程序消息  
- ✅ 搜索小程序
- ✅ 定时自动化任务
- ✅ 支持任何Python版本

## 📋 环境要求

- **微信版本**: ********* (与您当前版本匹配)
- **Python**: 任意版本 (推荐3.7+)
- **依赖**: requests, schedule
- **系统**: Windows 64位

## 🚀 快速开始

### 步骤1: 启动HTTP服务

#### 方法A: 图形界面 (推荐)
1. 运行 `wechat_http_tools\注入工具(图形界面版).exe`
2. 选择微信进程 (WeChat.exe)
3. 点击"注入"按钮
4. 看到"注入成功"提示

#### 方法B: 命令行
1. 运行 `wechat_http_tools\inject_tool.exe`
2. 按提示选择微信进程
3. 确认注入成功

#### 方法C: 一键启动
```bash
# 运行一键启动脚本
start_wechat_automation.bat
```

### 步骤2: 快速测试
```bash
# 测试API连接
python quick_test.py
```

### 步骤3: 开始自动化
```bash
# 启动完整自动化系统
python miniprogram_automation_final.py
```

## 🔧 详细配置

### 配置文件 (miniprogram_config.json)
```json
{
  "tasks": [
    {
      "name": "每日签到",
      "app_id": "wx1234567890",
      "times": ["09:00", "18:00"],
      "description": "每天9点和18点执行签到"
    }
  ],
  "api_url": "http://127.0.0.1:8203",
  "notification": true
}
```

### 任务配置说明
- **name**: 任务名称
- **app_id**: 小程序的AppId
- **times**: 执行时间列表 (24小时制)
- **description**: 任务描述

## 📖 API使用示例

### 基础连接测试
```python
import requests

# 检查登录状态
response = requests.get("http://127.0.0.1:8203/api/getLoginState")
print(response.json())

# 获取用户信息
response = requests.get("http://127.0.0.1:8203/api/getSelfInfo")
print(response.json())
```

### 小程序操作
```python
# 获取小程序code
data = {"app_id": "wx1234567890"}
response = requests.post(
    "http://127.0.0.1:8203/api/getSmallAppCode", 
    json=data
)
print(response.json())

# 发送消息
data = {
    "to_wx": "filehelper",
    "msg": "测试消息"
}
response = requests.post(
    "http://127.0.0.1:8203/api/sendText",
    json=data
)
print(response.json())
```

## 🛠️ 故障排除

### 常见问题

#### 1. "无法连接到API服务"
**解决方案:**
- 确保微信已启动并登录
- 运行注入工具
- 检查防火墙设置
- 以管理员身份运行

#### 2. "微信未登录"
**解决方案:**
- 确保微信已完全登录
- 重新扫码登录
- 重启微信后重新注入

#### 3. "注入失败"
**解决方案:**
- 以管理员身份运行注入工具
- 关闭杀毒软件
- 确保微信版本为*********
- 重启微信和注入工具

#### 4. "小程序code获取失败"
**解决方案:**
- 检查AppId是否正确
- 确保小程序存在且可访问
- 检查微信登录状态

## 📚 进阶使用

### 自定义任务
```python
from miniprogram_automation_final import MiniProgramAutomation

automation = MiniProgramAutomation()

# 自定义任务函数
def custom_task():
    # 获取小程序code
    code = automation.get_miniprogram_code("your_app_id")
    
    # 发送通知
    automation.send_notification(f"任务完成: {code}")

# 设置定时任务
import schedule
schedule.every().day.at("10:00").do(custom_task)
```

## 🔗 相关链接

- **API文档**: https://www.showdoc.com.cn/2228290215170955
- **项目地址**: https://github.com/mrsanshui/WeChatPYAPI
- **交流QQ群**: 54995858

## ⚠️ 注意事项

1. **合规使用**: 请遵守相关法律法规，仅用于个人学习和测试
2. **版本兼容**: 确保微信版本为*********
3. **安全性**: 注入工具可能被杀毒软件误报，请添加白名单
4. **稳定性**: 长时间运行建议定期重启
5. **备份**: 重要配置请及时备份
