@echo off
chcp 65001 >nul
title 微信小程序自动化启动器

echo ========================================
echo 微信小程序自动化启动器
echo 基于WeChatPYAPI HTTP接口
echo ========================================
echo.

echo 🔍 检查环境...

:: 检查微信是否运行
tasklist /FI "IMAGENAME eq WeChat.exe" 2>NUL | find /I /N "WeChat.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ 微信已运行
) else (
    echo ❌ 微信未运行
    echo 请先启动微信并登录
    pause
    exit /b 1
)

:: 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
) else (
    echo ✅ Python已安装
)

:: 检查依赖
python -c "import requests" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ 正在安装requests...
    pip install requests
)

echo.
echo 📋 选择操作:
echo 1. 启动图形界面注入工具
echo 2. 启动命令行注入工具  
echo 3. 直接测试HTTP API
echo 4. 查看使用指南
echo.

set /p choice=请选择 (1-4): 

if "%choice%"=="1" (
    echo 启动图形界面注入工具...
    start "" "wechat_http_tools\注入工具(图形界面版).exe"
    timeout /t 3 >nul
    goto test_api
) else if "%choice%"=="2" (
    echo 启动命令行注入工具...
    cd wechat_http_tools
    inject_tool.exe
    cd ..
    goto test_api
) else if "%choice%"=="3" (
    goto test_api
) else if "%choice%"=="4" (
    start "" "HTTP接口使用指南.md"
    goto end
) else (
    echo 无效选择
    pause
    exit /b 1
)

:test_api
echo.
echo 🧪 测试HTTP API连接...
timeout /t 5 >nul

python wechat_http_api_solution.py

:end
echo.
echo 程序结束
pause
