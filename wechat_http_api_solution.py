"""
基于WeChatPYAPI HTTP接口的微信小程序自动化解决方案
适用于任何Python版本，无需pyd文件
"""

import requests
import json
import time
import logging
from pathlib import Path

class WeChatHTTPAPI:
    """微信HTTP API封装类"""
    
    def __init__(self, base_url="http://127.0.0.1:8203", timeout=30):
        """
        初始化HTTP API
        :param base_url: API服务地址
        :param timeout: 请求超时时间
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
    def _request(self, endpoint, method="GET", data=None):
        """
        发送HTTP请求
        :param endpoint: API端点
        :param method: 请求方法
        :param data: 请求数据
        :return: 响应数据
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            if method.upper() == "GET":
                response = self.session.get(url, params=data, timeout=self.timeout)
            else:
                response = self.session.post(url, json=data, timeout=self.timeout)
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"HTTP请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            return None
    
    def check_connection(self):
        """检查API连接"""
        try:
            response = self._request("/")
            if response:
                self.logger.info("✅ API连接正常")
                return True
            else:
                self.logger.error("❌ API连接失败")
                return False
        except Exception as e:
            self.logger.error(f"❌ 连接检查失败: {e}")
            return False
    
    def get_login_state(self):
        """获取登录状态"""
        response = self._request("/api/getLoginState")
        if response and response.get("code") == 0:
            return response.get("data", False)
        return False
    
    def get_self_info(self):
        """获取个人信息"""
        response = self._request("/api/getSelfInfo")
        if response and response.get("code") == 0:
            return response.get("data")
        return None
    
    def send_text(self, to_wx, msg):
        """发送文本消息"""
        data = {
            "to_wx": to_wx,
            "msg": msg
        }
        response = self._request("/api/sendText", "POST", data)
        return response and response.get("code") == 0
    
    def get_small_app_code(self, app_id):
        """获取小程序code"""
        data = {"app_id": app_id}
        response = self._request("/api/getSmallAppCode", "POST", data)
        if response and response.get("code") == 0:
            return response.get("data")
        return None
    
    def send_xml(self, to_wx, xml_str):
        """发送XML消息"""
        data = {
            "to_wx": to_wx,
            "xml_str": xml_str
        }
        response = self._request("/api/sendXml", "POST", data)
        return response and response.get("code") == 0
    
    def pull_list(self, pull_type=1):
        """拉取列表"""
        data = {"pull_type": pull_type}
        response = self._request("/api/pullList", "POST", data)
        if response and response.get("code") == 0:
            return response.get("data", [])
        return []
    
    def wx_search(self, keyword, search_type=0):
        """微信搜索"""
        data = {
            "keyword": keyword,
            "search_type": search_type
        }
        response = self._request("/api/wxSearch", "POST", data)
        if response and response.get("code") == 0:
            return response.get("data")
        return None

class WeChatMiniProgramHTTPAutomation:
    """基于HTTP API的微信小程序自动化"""
    
    def __init__(self, api_url="http://127.0.0.1:8203"):
        """初始化"""
        self.api = WeChatHTTPAPI(api_url)
        self.logger = logging.getLogger(__name__)
        
    def check_environment(self):
        """检查环境"""
        print("🔍 检查环境...")
        
        # 检查API连接
        if not self.api.check_connection():
            print("❌ API服务未启动")
            print("请先启动WeChatPYAPI HTTP服务:")
            print("1. 运行 inject_tool.exe 或 注入工具(图形界面版).exe")
            print("2. 注入到微信进程")
            print("3. 确保HTTP服务在 http://127.0.0.1:8203 运行")
            return False
        
        # 检查登录状态
        if not self.api.get_login_state():
            print("❌ 微信未登录")
            print("请先登录微信")
            return False
        
        # 获取个人信息
        user_info = self.api.get_self_info()
        if user_info:
            print(f"✅ 微信已登录: {user_info.get('nickname', 'Unknown')}")
            return True
        else:
            print("❌ 无法获取用户信息")
            return False
    
    def test_miniprogram_functions(self):
        """测试小程序功能"""
        print("\n🧪 测试小程序功能...")
        
        # 测试获取小程序code
        test_app_id = "wx1234567890"  # 示例AppId
        print(f"测试获取小程序code: {test_app_id}")
        
        code = self.api.get_small_app_code(test_app_id)
        if code:
            print(f"✅ 获取小程序code成功: {code}")
        else:
            print("❌ 获取小程序code失败")
        
        # 测试搜索功能
        print("\n测试搜索小程序...")
        search_result = self.api.wx_search("文件传输助手")
        if search_result:
            print(f"✅ 搜索成功: {len(search_result)} 个结果")
        else:
            print("❌ 搜索失败")
    
    def send_miniprogram_message(self, to_wx, app_id, title="小程序", desc="点击打开"):
        """发送小程序消息"""
        # 构造小程序XML
        xml_content = f'''
        <msg>
            <appmsg appid="" sdkver="0">
                <title>{title}</title>
                <des>{desc}</des>
                <action>view</action>
                <type>33</type>
                <weappinfo>
                    <username>{app_id}</username>
                    <appid>{app_id}</appid>
                    <type>1</type>
                    <version>1</version>
                </weappinfo>
            </appmsg>
        </msg>
        '''
        
        return self.api.send_xml(to_wx, xml_content.strip())
    
    def auto_miniprogram_task(self, app_id, interval=3600):
        """自动化小程序任务"""
        print(f"\n🤖 开始自动化任务: {app_id}")
        print(f"执行间隔: {interval}秒")
        
        while True:
            try:
                print(f"\n⏰ {time.strftime('%Y-%m-%d %H:%M:%S')} - 执行任务")
                
                # 获取小程序code
                code = self.api.get_small_app_code(app_id)
                if code:
                    print(f"✅ 获取小程序code: {code}")
                    
                    # 可以在这里添加更多操作
                    # 比如发送消息通知
                    self.api.send_text("filehelper", f"小程序任务完成: {code}")
                else:
                    print("❌ 小程序任务失败")
                
                print(f"⏳ 等待 {interval} 秒...")
                time.sleep(interval)
                
            except KeyboardInterrupt:
                print("\n⏹️ 用户中断任务")
                break
            except Exception as e:
                print(f"❌ 任务执行出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再重试

def create_startup_guide():
    """创建启动指南"""
    guide_content = '''# WeChatPYAPI HTTP接口使用指南

## 1. 启动HTTP服务

### 方法1: 使用图形界面工具
1. 运行 `WeChatPYAPI-master/专业版/HTTP接口/注入工具(图形界面版).exe`
2. 选择微信进程
3. 点击注入
4. 确认HTTP服务启动在 http://127.0.0.1:8203

### 方法2: 使用命令行工具
1. 运行 `WeChatPYAPI-master/专业版/HTTP接口/inject_tool.exe`
2. 按提示操作

## 2. 测试连接
运行: `python wechat_http_api_solution.py`

## 3. API文档
访问: https://www.showdoc.com.cn/2228290215170955

## 4. 常见问题
- 确保微信版本为 3.9.10.19
- 确保微信已登录
- 确保防火墙允许HTTP服务
- 如果注入失败，尝试以管理员身份运行

## 5. 小程序自动化
成功连接后，可以使用以下功能:
- 获取小程序code
- 发送小程序消息
- 搜索小程序
- 自动化任务
'''
    
    with open("HTTP接口使用指南.md", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✅ 创建使用指南: HTTP接口使用指南.md")

def main():
    """主函数"""
    print("WeChatPYAPI HTTP接口测试")
    print("="*50)
    
    # 创建自动化实例
    automation = WeChatMiniProgramHTTPAutomation()
    
    # 检查环境
    if not automation.check_environment():
        print("\n❌ 环境检查失败")
        print("请按照以下步骤操作:")
        print("1. 确保微信已启动并登录")
        print("2. 运行注入工具启动HTTP服务")
        print("3. 重新运行此脚本")
        
        # 创建启动指南
        create_startup_guide()
        return
    
    print("\n✅ 环境检查通过!")
    
    # 测试小程序功能
    automation.test_miniprogram_functions()
    
    # 交互式菜单
    while True:
        print("\n" + "="*30)
        print("选择操作:")
        print("1. 测试获取小程序code")
        print("2. 发送小程序消息")
        print("3. 搜索小程序")
        print("4. 开始自动化任务")
        print("5. 退出")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == "1":
            app_id = input("请输入小程序AppId: ").strip()
            if app_id:
                code = automation.api.get_small_app_code(app_id)
                print(f"结果: {code}")
        
        elif choice == "2":
            to_wx = input("发送给 (微信ID): ").strip()
            app_id = input("小程序AppId: ").strip()
            if to_wx and app_id:
                result = automation.send_miniprogram_message(to_wx, app_id)
                print(f"发送结果: {'成功' if result else '失败'}")
        
        elif choice == "3":
            keyword = input("搜索关键词: ").strip()
            if keyword:
                result = automation.api.wx_search(keyword)
                print(f"搜索结果: {result}")
        
        elif choice == "4":
            app_id = input("小程序AppId: ").strip()
            interval = input("执行间隔(秒，默认3600): ").strip()
            interval = int(interval) if interval.isdigit() else 3600
            
            if app_id:
                automation.auto_miniprogram_task(app_id, interval)
        
        elif choice == "5":
            print("退出程序")
            break
        
        else:
            print("无效选择")

if __name__ == "__main__":
    main()
