"""
快速测试WeChatPYAPI HTTP接口连接
"""

import requests
import json
import time

def test_connection():
    """测试API连接"""
    print("🔍 测试API连接...")
    
    api_url = "http://127.0.0.1:8203"
    
    try:
        # 测试基本连接
        response = requests.get(f"{api_url}/", timeout=5)
        print(f"✅ API服务响应: {response.status_code}")
        
        # 测试登录状态
        response = requests.get(f"{api_url}/api/getLoginState", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0 and data.get("data"):
                print("✅ 微信已登录")
                return True
            else:
                print("❌ 微信未登录")
                return False
        else:
            print(f"❌ 登录状态检查失败: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务")
        print("请确保:")
        print("1. 微信已启动并登录")
        print("2. 已运行注入工具")
        print("3. HTTP服务在 http://127.0.0.1:8203 运行")
        return False
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def test_user_info():
    """测试获取用户信息"""
    print("\n🔍 测试获取用户信息...")
    
    try:
        response = requests.get("http://127.0.0.1:8203/api/getSelfInfo", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get("code") == 0:
                user_info = data.get("data", {})
                print(f"✅ 用户昵称: {user_info.get('nickname', 'Unknown')}")
                print(f"✅ 微信ID: {user_info.get('wx_id', 'Unknown')}")
                return True
            else:
                print(f"❌ 获取用户信息失败: {data.get('msg', 'Unknown error')}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 获取用户信息失败: {e}")
        return False

def test_miniprogram_api():
    """测试小程序API"""
    print("\n🔍 测试小程序API...")
    
    # 测试获取小程序code
    test_app_id = "wx1234567890"  # 示例AppId
    
    try:
        data = {"app_id": test_app_id}
        response = requests.post(
            "http://127.0.0.1:8203/api/getSmallAppCode", 
            json=data, 
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                code = result.get("data")
                print(f"✅ 小程序API可用，返回code: {code}")
                return True
            else:
                print(f"⚠️ 小程序API响应: {result.get('msg', 'Unknown')}")
                print("这可能是因为AppId无效，但API本身是可用的")
                return True
        else:
            print(f"❌ 小程序API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 小程序API测试失败: {e}")
        return False

def test_send_message():
    """测试发送消息"""
    print("\n🔍 测试发送消息...")
    
    try:
        data = {
            "to_wx": "filehelper",  # 文件传输助手
            "msg": "WeChatPYAPI测试消息 - " + time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        response = requests.post(
            "http://127.0.0.1:8203/api/sendText",
            json=data,
            timeout=5
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == 0:
                print("✅ 消息发送成功")
                print("请检查文件传输助手是否收到测试消息")
                return True
            else:
                print(f"❌ 消息发送失败: {result.get('msg', 'Unknown')}")
                return False
        else:
            print(f"❌ 发送请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 发送消息失败: {e}")
        return False

def main():
    """主函数"""
    print("WeChatPYAPI HTTP接口快速测试")
    print("="*50)
    
    # 基础连接测试
    if not test_connection():
        print("\n❌ 基础连接测试失败")
        print("\n解决方案:")
        print("1. 确保微信已启动并登录")
        print("2. 运行注入工具:")
        print("   - 图形界面: wechat_http_tools\\注入工具(图形界面版).exe")
        print("   - 命令行: wechat_http_tools\\inject_tool.exe")
        print("3. 确保HTTP服务启动在 http://127.0.0.1:8203")
        return
    
    # 用户信息测试
    if not test_user_info():
        print("\n❌ 用户信息测试失败")
        return
    
    # 小程序API测试
    test_miniprogram_api()
    
    # 发送消息测试
    test_send_message()
    
    print("\n" + "="*50)
    print("✅ 快速测试完成!")
    print("="*50)
    print("如果所有测试都通过，您可以:")
    print("1. 运行完整测试: python wechat_http_api_solution.py")
    print("2. 开始小程序自动化开发")
    print("3. 查看API文档: https://www.showdoc.com.cn/2228290215170955")

if __name__ == "__main__":
    main()
