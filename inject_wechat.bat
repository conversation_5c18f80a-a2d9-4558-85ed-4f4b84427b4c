@echo off
chcp 65001 >nul
title 微信注入工具

echo ========================================
echo 微信注入工具
echo ========================================
echo.

echo 🔍 检查微信进程...
tasklist /FI "IMAGENAME eq WeChat.exe" | find "WeChat.exe"
if %errorlevel% neq 0 (
    echo ❌ 微信未运行，请先启动微信
    pause
    exit /b 1
)

echo ✅ 微信进程已找到
echo.

echo 📋 选择注入方式:
echo 1. 图形界面注入工具
echo 2. 命令行注入工具
echo 3. 以管理员身份运行图形界面工具
echo.

set /p choice=请选择 (1-3): 

if "%choice%"=="1" (
    echo 启动图形界面注入工具...
    start "" "wechat_http_tools\注入工具(图形界面版).exe"
) else if "%choice%"=="2" (
    echo 启动命令行注入工具...
    cd wechat_http_tools
    inject_tool.exe
    cd ..
) else if "%choice%"=="3" (
    echo 以管理员身份启动图形界面工具...
    powershell -Command "Start-Process 'wechat_http_tools\注入工具(图形界面版).exe' -Verb RunAs"
) else (
    echo 无效选择
    pause
    exit /b 1
)

echo.
echo ⏳ 等待5秒后测试连接...
timeout /t 5 >nul

echo 🧪 测试API连接...
python quick_test.py

echo.
echo 注入完成！
pause
