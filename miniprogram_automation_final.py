"""
微信小程序自动化最终解决方案
基于WeChatPYAPI HTTP接口
"""

import requests
import json
import time
import schedule
import threading
from datetime import datetime
from pathlib import Path

class MiniProgramAutomation:
    """小程序自动化类"""
    
    def __init__(self, api_base="http://127.0.0.1:8203"):
        self.api_base = api_base
        self.session = requests.Session()
        self.running = False
        
    def api_request(self, endpoint, method="GET", data=None):
        """API请求封装"""
        url = f"{self.api_base}/{endpoint.lstrip('/')}"
        
        try:
            if method.upper() == "GET":
                response = self.session.get(url, params=data, timeout=10)
            else:
                response = self.session.post(url, json=data, timeout=10)
            
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"API请求失败: {e}")
            return None
    
    def check_status(self):
        """检查微信状态"""
        # 检查登录状态
        result = self.api_request("/api/getLoginState")
        if not (result and result.get("code") == 0 and result.get("data")):
            return False, "微信未登录"
        
        # 获取用户信息
        user_info = self.api_request("/api/getSelfInfo")
        if not (user_info and user_info.get("code") == 0):
            return False, "无法获取用户信息"
        
        nickname = user_info.get("data", {}).get("nickname", "Unknown")
        return True, f"微信已登录: {nickname}"
    
    def get_miniprogram_code(self, app_id):
        """获取小程序code"""
        data = {"app_id": app_id}
        result = self.api_request("/api/getSmallAppCode", "POST", data)
        
        if result and result.get("code") == 0:
            return result.get("data")
        else:
            error_msg = result.get("msg", "Unknown error") if result else "Request failed"
            print(f"获取小程序code失败: {error_msg}")
            return None
    
    def send_notification(self, message):
        """发送通知消息到文件传输助手"""
        data = {
            "to_wx": "filehelper",
            "msg": f"[小程序自动化] {message}"
        }
        
        result = self.api_request("/api/sendText", "POST", data)
        return result and result.get("code") == 0
    
    def search_miniprogram(self, keyword):
        """搜索小程序"""
        data = {
            "keyword": keyword,
            "search_type": 0
        }
        
        result = self.api_request("/api/wxSearch", "POST", data)
        if result and result.get("code") == 0:
            return result.get("data")
        return None
    
    def daily_miniprogram_task(self, app_id, task_name="默认任务"):
        """每日小程序任务"""
        print(f"\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 执行任务: {task_name}")
        
        # 检查状态
        status_ok, status_msg = self.check_status()
        if not status_ok:
            print(f"❌ 状态检查失败: {status_msg}")
            self.send_notification(f"任务失败: {status_msg}")
            return False
        
        # 获取小程序code
        code = self.get_miniprogram_code(app_id)
        if code:
            print(f"✅ 获取小程序code成功: {code}")
            self.send_notification(f"任务完成: {task_name} - Code: {code}")
            
            # 这里可以添加更多小程序相关操作
            # 比如模拟点击、数据提交等
            
            return True
        else:
            print(f"❌ 获取小程序code失败")
            self.send_notification(f"任务失败: {task_name} - 无法获取code")
            return False
    
    def setup_schedule(self, app_id, times=["09:00"], task_name="每日签到"):
        """设置定时任务"""
        print(f"📅 设置定时任务: {task_name}")
        print(f"⏰ 执行时间: {', '.join(times)}")
        
        for time_str in times:
            schedule.every().day.at(time_str).do(
                self.daily_miniprogram_task, 
                app_id=app_id, 
                task_name=task_name
            )
        
        print(f"✅ 定时任务设置完成")
    
    def run_scheduler(self):
        """运行定时任务调度器"""
        print("🚀 启动定时任务调度器...")
        self.running = True
        
        while self.running:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    
    def start_automation(self, config):
        """启动自动化"""
        print("🎯 启动小程序自动化")
        print("="*50)
        
        # 检查初始状态
        status_ok, status_msg = self.check_status()
        if not status_ok:
            print(f"❌ 初始状态检查失败: {status_msg}")
            return False
        
        print(f"✅ {status_msg}")
        
        # 设置定时任务
        for task in config.get("tasks", []):
            self.setup_schedule(
                app_id=task["app_id"],
                times=task.get("times", ["09:00"]),
                task_name=task.get("name", "未命名任务")
            )
        
        # 发送启动通知
        self.send_notification("自动化系统已启动")
        
        # 启动调度器
        scheduler_thread = threading.Thread(target=self.run_scheduler)
        scheduler_thread.daemon = True
        scheduler_thread.start()
        
        return True
    
    def stop_automation(self):
        """停止自动化"""
        print("⏹️ 停止自动化...")
        self.running = False
        schedule.clear()
        self.send_notification("自动化系统已停止")

def load_config():
    """加载配置文件"""
    config_file = Path("miniprogram_config.json")
    
    if config_file.exists():
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            print(f"配置文件加载失败: {e}")
    
    # 默认配置
    default_config = {
        "tasks": [
            {
                "name": "示例小程序任务",
                "app_id": "wx1234567890",
                "times": ["09:00", "18:00"],
                "description": "每天9点和18点执行"
            }
        ],
        "api_url": "http://127.0.0.1:8203",
        "notification": True
    }
    
    # 保存默认配置
    with open(config_file, "w", encoding="utf-8") as f:
        json.dump(default_config, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 创建默认配置文件: {config_file}")
    return default_config

def interactive_setup():
    """交互式设置"""
    print("🔧 交互式设置小程序自动化")
    print("="*40)
    
    tasks = []
    
    while True:
        print(f"\n📝 配置任务 #{len(tasks) + 1}")
        
        name = input("任务名称: ").strip()
        if not name:
            break
        
        app_id = input("小程序AppId: ").strip()
        if not app_id:
            print("AppId不能为空")
            continue
        
        times_input = input("执行时间 (格式: 09:00,18:00): ").strip()
        times = [t.strip() for t in times_input.split(",") if t.strip()]
        if not times:
            times = ["09:00"]
        
        task = {
            "name": name,
            "app_id": app_id,
            "times": times
        }
        
        tasks.append(task)
        print(f"✅ 任务已添加: {name}")
        
        if input("继续添加任务? (y/n): ").strip().lower() != 'y':
            break
    
    if tasks:
        config = {
            "tasks": tasks,
            "api_url": "http://127.0.0.1:8203",
            "notification": True
        }
        
        with open("miniprogram_config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 配置已保存: miniprogram_config.json")
        return config
    
    return None

def main():
    """主函数"""
    print("微信小程序自动化系统")
    print("="*50)
    
    # 创建自动化实例
    automation = MiniProgramAutomation()
    
    # 检查连接
    status_ok, status_msg = automation.check_status()
    if not status_ok:
        print(f"❌ {status_msg}")
        print("\n请确保:")
        print("1. 微信已启动并登录")
        print("2. HTTP API服务已启动")
        print("3. 运行: python quick_test.py 进行测试")
        return
    
    print(f"✅ {status_msg}")
    
    # 菜单选择
    while True:
        print("\n" + "="*30)
        print("选择操作:")
        print("1. 立即测试小程序功能")
        print("2. 交互式配置自动化任务")
        print("3. 使用现有配置启动自动化")
        print("4. 查看当前配置")
        print("5. 退出")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == "1":
            app_id = input("请输入小程序AppId: ").strip()
            if app_id:
                automation.daily_miniprogram_task(app_id, "手动测试")
        
        elif choice == "2":
            config = interactive_setup()
            if config:
                if input("立即启动自动化? (y/n): ").strip().lower() == 'y':
                    if automation.start_automation(config):
                        print("✅ 自动化已启动，按Ctrl+C停止")
                        try:
                            while True:
                                time.sleep(1)
                        except KeyboardInterrupt:
                            automation.stop_automation()
        
        elif choice == "3":
            config = load_config()
            if automation.start_automation(config):
                print("✅ 自动化已启动，按Ctrl+C停止")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    automation.stop_automation()
        
        elif choice == "4":
            config = load_config()
            print("\n📋 当前配置:")
            print(json.dumps(config, ensure_ascii=False, indent=2))
        
        elif choice == "5":
            print("退出程序")
            break
        
        else:
            print("无效选择")

if __name__ == "__main__":
    try:
        # 安装schedule库
        try:
            import schedule
        except ImportError:
            print("正在安装schedule库...")
            import subprocess
            subprocess.check_call(["pip", "install", "schedule"])
            import schedule
        
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序出错: {e}")
