"""
高级调试工具 - 检查注入失败的原因
"""

import subprocess
import time
import os
import sys
import ctypes
from pathlib import Path

def is_admin():
    """检查是否以管理员身份运行"""
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def check_wechat_version():
    """检查微信版本"""
    print("🔍 检查微信版本...")
    
    try:
        # 查找微信安装路径
        wechat_path = Path("C:/Program Files/Tencent/WeChat/WeChat.exe")
        if not wechat_path.exists():
            wechat_path = Path("C:/Program Files (x86)/Tencent/WeChat/WeChat.exe")
        
        if wechat_path.exists():
            # 获取文件版本信息
            result = subprocess.run([
                "powershell", 
                f"(Get-ItemProperty '{wechat_path}').VersionInfo.FileVersion"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"✅ 微信版本: {version}")
                
                if "*********" in version:
                    print("✅ 版本匹配专业版要求")
                    return True
                else:
                    print("⚠️ 版本可能不匹配，专业版要求*********")
                    return False
            else:
                print("❌ 无法获取版本信息")
                return False
        else:
            print("❌ 未找到微信安装路径")
            return False
            
    except Exception as e:
        print(f"❌ 检查版本失败: {e}")
        return False

def test_dll_compatibility():
    """测试DLL兼容性"""
    print("\n🔍 测试DLL兼容性...")
    
    dll_path = Path("wechat_http_tools/WeChatApi.dll")
    if not dll_path.exists():
        print("❌ DLL文件不存在")
        return False
    
    try:
        # 尝试加载DLL
        dll = ctypes.WinDLL(str(dll_path))
        print("✅ DLL可以加载")
        return True
    except Exception as e:
        print(f"❌ DLL加载失败: {e}")
        print("可能原因:")
        print("1. DLL与系统架构不匹配")
        print("2. 缺少依赖库")
        print("3. DLL文件损坏")
        return False

def check_injection_prerequisites():
    """检查注入前提条件"""
    print("\n🔍 检查注入前提条件...")
    
    checks = []
    
    # 1. 管理员权限
    if is_admin():
        print("✅ 以管理员身份运行")
        checks.append(True)
    else:
        print("❌ 未以管理员身份运行")
        print("建议: 以管理员身份重新运行此脚本")
        checks.append(False)
    
    # 2. 微信版本
    version_ok = check_wechat_version()
    checks.append(version_ok)
    
    # 3. DLL兼容性
    dll_ok = test_dll_compatibility()
    checks.append(dll_ok)
    
    # 4. 进程权限
    try:
        import psutil
        wechat_procs = [p for p in psutil.process_iter(['pid', 'name']) 
                       if p.info['name'] == 'WeChat.exe']
        
        if wechat_procs:
            wechat_proc = psutil.Process(wechat_procs[0].info['pid'])
            # 尝试访问进程信息
            wechat_proc.memory_info()
            print("✅ 可以访问微信进程")
            checks.append(True)
        else:
            print("❌ 未找到微信进程")
            checks.append(False)
    except Exception as e:
        print(f"❌ 无法访问微信进程: {e}")
        checks.append(False)
    
    return all(checks)

def try_alternative_injection():
    """尝试替代注入方法"""
    print("\n🔧 尝试替代注入方法...")
    
    # 方法1: 直接运行图形界面工具
    print("方法1: 启动图形界面工具")
    try:
        gui_tool = Path("wechat_http_tools/注入工具(图形界面版).exe")
        if gui_tool.exists():
            if is_admin():
                subprocess.Popen([str(gui_tool)])
                print("✅ 图形界面工具已启动")
                print("请手动选择微信进程并注入")
                return True
            else:
                # 以管理员身份启动
                subprocess.run([
                    "powershell", 
                    "-Command", 
                    f"Start-Process '{gui_tool.absolute()}' -Verb RunAs"
                ])
                print("✅ 以管理员身份启动图形界面工具")
                return True
        else:
            print("❌ 图形界面工具不存在")
    except Exception as e:
        print(f"❌ 启动图形界面工具失败: {e}")
    
    return False

def create_manual_injection_guide():
    """创建手动注入指南"""
    print("\n📖 创建详细的手动注入指南...")
    
    guide_content = '''# 微信注入详细指南

## 问题诊断

根据测试结果，可能的问题包括:
1. 权限不足 - 需要管理员权限
2. 微信版本不匹配 - 需要*********版本
3. DLL兼容性问题
4. 安全软件阻挡

## 解决方案

### 方案1: 手动图形界面注入 (推荐)

1. **以管理员身份运行**
   - 右键点击 `wechat_http_tools\\注入工具(图形界面版).exe`
   - 选择"以管理员身份运行"

2. **选择正确的进程**
   - 在进程列表中找到 `WeChat.exe`
   - 确保选择的是主进程 (通常内存占用最大)
   - PID应该是22728左右

3. **执行注入**
   - 点击"注入"按钮
   - 等待"注入成功"提示
   - 如果失败，尝试重启微信后重新注入

### 方案2: 检查微信版本

1. **确认微信版本**
   - 打开微信 -> 设置 -> 关于微信
   - 确认版本为 *********
   - 如果不是，请下载正确版本

2. **下载地址**
   - 百度网盘: https://pan.baidu.com/s/1dxBuvDgAeI0mFjGsY6NVNA
   - 提取码: sszs

### 方案3: 替代方案

如果注入始终失败，可以考虑:
1. 使用社区版 (支持3.6.0.18)
2. 联系项目作者获取支持
3. 加入QQ群: 54995858

## 验证注入成功

注入成功后，运行以下命令验证:
```bash
python quick_test.py
```

应该看到:
- ✅ API服务响应: 200
- ✅ 微信已登录
- ✅ 用户昵称: [您的昵称]

## 常见错误及解决

### "注入失败"
- 以管理员身份运行注入工具
- 关闭杀毒软件
- 重启微信

### "找不到进程"
- 确保微信已完全启动
- 选择正确的WeChat.exe进程

### "权限不足"
- 必须以管理员身份运行
- 检查UAC设置

### "DLL加载失败"
- 检查系统架构 (需要64位)
- 安装Visual C++ Redistributable
- 重新下载项目文件
'''
    
    with open("注入问题解决指南.md", "w", encoding="utf-8") as f:
        f.write(guide_content)
    
    print("✅ 创建指南文件: 注入问题解决指南.md")

def main():
    """主函数"""
    print("WeChatPYAPI 高级调试工具")
    print("="*40)
    
    print(f"当前权限: {'管理员' if is_admin() else '普通用户'}")
    
    # 检查前提条件
    if check_injection_prerequisites():
        print("\n✅ 所有前提条件满足")
        print("注入应该可以成功")
        
        # 尝试替代注入
        if try_alternative_injection():
            print("\n⏳ 请在图形界面工具中手动完成注入")
            print("注入成功后运行: python quick_test.py")
        
    else:
        print("\n❌ 存在问题，需要解决")
        
        if not is_admin():
            print("\n🔧 尝试以管理员身份重新运行...")
            try:
                # 以管理员身份重新运行此脚本
                subprocess.run([
                    "powershell", 
                    "-Command", 
                    f"Start-Process python -ArgumentList '{__file__}' -Verb RunAs"
                ])
                return
            except Exception as e:
                print(f"❌ 无法以管理员身份运行: {e}")
    
    # 创建手动指南
    create_manual_injection_guide()
    
    print("\n" + "="*40)
    print("调试完成")
    print("="*40)
    print("下一步:")
    print("1. 查看 '注入问题解决指南.md'")
    print("2. 以管理员身份运行注入工具")
    print("3. 手动选择微信进程并注入")
    print("4. 运行 python quick_test.py 验证")

if __name__ == "__main__":
    try:
        main()
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")
        input("\n按回车键退出...")
