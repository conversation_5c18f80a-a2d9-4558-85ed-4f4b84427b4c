"""
直接注入微信进程
"""

import subprocess
import time
import os
import psutil
from pathlib import Path

def find_main_wechat_process():
    """找到主要的微信进程"""
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            if (proc.info['name'] == 'WeChat.exe' and 
                proc.info['exe'] and 
                'Program Files' in proc.info['exe']):
                return proc.info
        except:
            continue
    return None

def inject_with_cli_tool():
    """使用命令行工具注入"""
    print("🚀 使用命令行工具注入...")
    
    # 找到主微信进程
    wechat_proc = find_main_wechat_process()
    if not wechat_proc:
        print("❌ 未找到主微信进程")
        return False
    
    print(f"✅ 找到主微信进程: PID {wechat_proc['pid']}")
    
    # 切换到工具目录
    tools_dir = Path("wechat_http_tools")
    if not tools_dir.exists():
        print("❌ 工具目录不存在")
        return False
    
    inject_tool = tools_dir / "inject_tool.exe"
    if not inject_tool.exists():
        print("❌ 注入工具不存在")
        return False
    
    print("🔧 启动注入工具...")
    
    try:
        # 切换到工具目录并运行
        os.chdir(tools_dir)
        
        # 创建自动化输入脚本
        auto_input = f"{wechat_proc['pid']}\n"  # 输入PID
        
        # 运行注入工具
        process = subprocess.Popen(
            ["inject_tool.exe"],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 发送输入
        stdout, stderr = process.communicate(input=auto_input, timeout=30)
        
        print("注入工具输出:")
        print(stdout)
        
        if stderr:
            print("错误输出:")
            print(stderr)
        
        # 切换回原目录
        os.chdir("..")
        
        return process.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ 注入工具超时")
        process.kill()
        os.chdir("..")
        return False
    except Exception as e:
        print(f"❌ 注入失败: {e}")
        os.chdir("..")
        return False

def test_injection_result():
    """测试注入结果"""
    print("\n🧪 测试注入结果...")
    
    import requests
    
    # 等待服务启动
    for i in range(10):
        try:
            response = requests.get("http://127.0.0.1:8203/", timeout=2)
            print(f"✅ API服务已启动: {response.status_code}")
            
            # 测试登录状态
            login_response = requests.get("http://127.0.0.1:8203/api/getLoginState", timeout=2)
            if login_response.status_code == 200:
                data = login_response.json()
                print(f"登录状态: {data}")
                
                if data.get("code") == 0 and data.get("data"):
                    print("✅ 微信已登录，注入成功！")
                    return True
                else:
                    print("⚠️ 微信未登录")
                    return False
            
        except requests.exceptions.ConnectionError:
            print(f"⏳ 等待API服务启动... ({i+1}/10)")
            time.sleep(2)
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            time.sleep(2)
    
    print("❌ API服务未启动")
    return False

def manual_injection_guide():
    """手动注入指南"""
    print("\n📖 手动注入指南")
    print("="*40)
    
    wechat_proc = find_main_wechat_process()
    if wechat_proc:
        print(f"目标进程: WeChat.exe (PID: {wechat_proc['pid']})")
        print(f"进程路径: {wechat_proc['exe']}")
    
    print("\n步骤:")
    print("1. 以管理员身份运行: wechat_http_tools\\注入工具(图形界面版).exe")
    print("2. 在进程列表中找到 WeChat.exe")
    print(f"3. 选择 PID 为 {wechat_proc['pid'] if wechat_proc else 'XXXX'} 的进程")
    print("4. 点击'注入'按钮")
    print("5. 等待'注入成功'提示")
    print("6. 运行: python quick_test.py 验证")

def create_injection_script():
    """创建注入脚本"""
    print("\n📝 创建自动注入脚本...")
    
    wechat_proc = find_main_wechat_process()
    if not wechat_proc:
        print("❌ 未找到微信进程")
        return
    
    script_content = f'''@echo off
chcp 65001 >nul
title 自动注入微信

echo 自动注入微信进程
echo 目标PID: {wechat_proc['pid']}
echo.

cd wechat_http_tools

echo {wechat_proc['pid']} | inject_tool.exe

cd ..

echo.
echo 注入完成，测试连接...
timeout /t 3 >nul

python quick_test.py

pause
'''
    
    with open("auto_inject.bat", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ 创建自动注入脚本: auto_inject.bat")

def main():
    """主函数"""
    print("微信直接注入工具")
    print("="*30)
    
    # 检查微信进程
    wechat_proc = find_main_wechat_process()
    if not wechat_proc:
        print("❌ 未找到主微信进程")
        print("请确保微信已启动并登录")
        return
    
    print(f"✅ 找到微信进程: PID {wechat_proc['pid']}")
    
    # 创建自动注入脚本
    create_injection_script()
    
    print("\n选择注入方式:")
    print("1. 自动注入 (命令行)")
    print("2. 手动注入指南")
    print("3. 运行自动注入脚本")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        if inject_with_cli_tool():
            print("✅ 注入成功")
            test_injection_result()
        else:
            print("❌ 注入失败")
            manual_injection_guide()
    
    elif choice == "2":
        manual_injection_guide()
    
    elif choice == "3":
        print("运行自动注入脚本...")
        subprocess.run(["auto_inject.bat"], shell=True)
    
    else:
        print("无效选择")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"程序出错: {e}")
