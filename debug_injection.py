"""
WeChatPYAPI 注入调试工具
"""

import subprocess
import time
import requests
import psutil
import os
from pathlib import Path

def check_wechat_process():
    """检查微信进程"""
    print("🔍 检查微信进程...")
    
    wechat_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            if proc.info['name'] and 'wechat' in proc.info['name'].lower():
                wechat_processes.append(proc.info)
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if wechat_processes:
        for proc in wechat_processes:
            print(f"✅ 找到微信进程: {proc['name']} (PID: {proc['pid']})")
            if proc['exe']:
                print(f"   路径: {proc['exe']}")
        return wechat_processes
    else:
        print("❌ 未找到微信进程")
        return []

def check_injection_tools():
    """检查注入工具"""
    print("\n🔍 检查注入工具...")
    
    tools_dir = Path("wechat_http_tools")
    if not tools_dir.exists():
        print("❌ 注入工具目录不存在")
        return False
    
    gui_tool = tools_dir / "注入工具(图形界面版).exe"
    cli_tool = tools_dir / "inject_tool.exe"
    dll_file = tools_dir / "WeChatApi.dll"
    
    print(f"图形界面工具: {'✅' if gui_tool.exists() else '❌'} {gui_tool}")
    print(f"命令行工具: {'✅' if cli_tool.exists() else '❌'} {cli_tool}")
    print(f"API库文件: {'✅' if dll_file.exists() else '❌'} {dll_file}")
    
    return gui_tool.exists() and cli_tool.exists() and dll_file.exists()

def check_api_service():
    """检查API服务状态"""
    print("\n🔍 检查API服务...")
    
    ports_to_check = [8203, 8080, 8888, 9999]  # 常见端口
    
    for port in ports_to_check:
        try:
            url = f"http://127.0.0.1:{port}"
            response = requests.get(url, timeout=2)
            print(f"✅ 端口 {port} 有响应: {response.status_code}")
            
            # 尝试获取登录状态
            try:
                login_response = requests.get(f"{url}/api/getLoginState", timeout=2)
                print(f"   登录状态API: {login_response.status_code}")
                if login_response.status_code == 200:
                    data = login_response.json()
                    print(f"   响应数据: {data}")
            except:
                print(f"   登录状态API: 无响应")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 端口 {port} 无响应")
        except Exception as e:
            print(f"❌ 端口 {port} 错误: {e}")

def try_manual_injection():
    """尝试手动注入"""
    print("\n🔧 尝试手动注入...")
    
    wechat_processes = check_wechat_process()
    if not wechat_processes:
        print("❌ 没有微信进程，无法注入")
        return False
    
    wechat_pid = wechat_processes[0]['pid']
    print(f"目标进程PID: {wechat_pid}")
    
    # 尝试使用命令行工具注入
    cli_tool = Path("wechat_http_tools/inject_tool.exe")
    if cli_tool.exists():
        print("🚀 启动命令行注入工具...")
        try:
            # 以管理员权限运行
            result = subprocess.run([
                "powershell", 
                "-Command", 
                f"Start-Process '{cli_tool.absolute()}' -Verb RunAs"
            ], capture_output=True, text=True, timeout=10)
            
            print(f"启动结果: {result.returncode}")
            if result.stdout:
                print(f"输出: {result.stdout}")
            if result.stderr:
                print(f"错误: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ 命令行工具启动中...")
        except Exception as e:
            print(f"❌ 启动失败: {e}")
    
    return True

def create_injection_batch():
    """创建注入批处理文件"""
    print("\n📝 创建注入批处理文件...")
    
    batch_content = '''@echo off
chcp 65001 >nul
title 微信注入工具

echo ========================================
echo 微信注入工具
echo ========================================
echo.

echo 🔍 检查微信进程...
tasklist /FI "IMAGENAME eq WeChat.exe" | find "WeChat.exe"
if %errorlevel% neq 0 (
    echo ❌ 微信未运行，请先启动微信
    pause
    exit /b 1
)

echo ✅ 微信进程已找到
echo.

echo 📋 选择注入方式:
echo 1. 图形界面注入工具
echo 2. 命令行注入工具
echo 3. 以管理员身份运行图形界面工具
echo.

set /p choice=请选择 (1-3): 

if "%choice%"=="1" (
    echo 启动图形界面注入工具...
    start "" "wechat_http_tools\\注入工具(图形界面版).exe"
) else if "%choice%"=="2" (
    echo 启动命令行注入工具...
    cd wechat_http_tools
    inject_tool.exe
    cd ..
) else if "%choice%"=="3" (
    echo 以管理员身份启动图形界面工具...
    powershell -Command "Start-Process 'wechat_http_tools\\注入工具(图形界面版).exe' -Verb RunAs"
) else (
    echo 无效选择
    pause
    exit /b 1
)

echo.
echo ⏳ 等待5秒后测试连接...
timeout /t 5 >nul

echo 🧪 测试API连接...
python quick_test.py

echo.
echo 注入完成！
pause
'''
    
    with open("inject_wechat.bat", "w", encoding="utf-8") as f:
        f.write(batch_content)
    
    print("✅ 创建批处理文件: inject_wechat.bat")

def check_firewall_and_antivirus():
    """检查防火墙和杀毒软件"""
    print("\n🛡️ 检查防火墙和安全软件...")
    
    # 检查Windows防火墙
    try:
        result = subprocess.run([
            "netsh", "advfirewall", "show", "allprofiles", "state"
        ], capture_output=True, text=True, timeout=10)
        
        if "ON" in result.stdout:
            print("⚠️ Windows防火墙已启用")
            print("建议: 添加注入工具到防火墙例外")
        else:
            print("✅ Windows防火墙已关闭")
            
    except Exception as e:
        print(f"❌ 无法检查防火墙状态: {e}")
    
    # 检查常见杀毒软件进程
    antivirus_processes = [
        "360tray.exe", "360sd.exe", "QQPCTray.exe", "KSafeTray.exe",
        "avp.exe", "mcshield.exe", "avgnt.exe", "avguard.exe"
    ]
    
    running_av = []
    for proc in psutil.process_iter(['name']):
        try:
            if proc.info['name'] and proc.info['name'].lower() in [av.lower() for av in antivirus_processes]:
                running_av.append(proc.info['name'])
        except:
            continue
    
    if running_av:
        print(f"⚠️ 检测到杀毒软件: {', '.join(running_av)}")
        print("建议: 将注入工具添加到杀毒软件白名单")
    else:
        print("✅ 未检测到常见杀毒软件")

def advanced_debug():
    """高级调试"""
    print("\n🔬 高级调试信息...")
    
    # 检查端口占用
    print("检查端口占用:")
    try:
        result = subprocess.run([
            "netstat", "-ano", "|", "findstr", ":8203"
        ], shell=True, capture_output=True, text=True)
        
        if result.stdout.strip():
            print(f"端口8203占用情况:\n{result.stdout}")
        else:
            print("端口8203未被占用")
    except Exception as e:
        print(f"无法检查端口占用: {e}")
    
    # 检查DLL文件
    dll_path = Path("wechat_http_tools/WeChatApi.dll")
    if dll_path.exists():
        stat = dll_path.stat()
        print(f"DLL文件信息:")
        print(f"  大小: {stat.st_size} 字节")
        print(f"  修改时间: {time.ctime(stat.st_mtime)}")
    
    # 检查系统架构
    import platform
    print(f"系统架构: {platform.architecture()}")
    print(f"系统版本: {platform.system()} {platform.release()}")

def main():
    """主函数"""
    print("WeChatPYAPI 注入调试工具")
    print("="*50)
    
    # 基础检查
    wechat_processes = check_wechat_process()
    tools_available = check_injection_tools()
    
    if not wechat_processes:
        print("\n❌ 微信未运行，请先启动微信并登录")
        return
    
    if not tools_available:
        print("\n❌ 注入工具不完整")
        return
    
    # 检查API服务
    check_api_service()
    
    # 检查安全软件
    check_firewall_and_antivirus()
    
    # 高级调试
    advanced_debug()
    
    # 创建批处理文件
    create_injection_batch()
    
    print("\n" + "="*50)
    print("调试完成")
    print("="*50)
    print("下一步建议:")
    print("1. 运行: inject_wechat.bat")
    print("2. 选择以管理员身份运行注入工具")
    print("3. 在注入工具中选择微信进程并注入")
    print("4. 注入成功后运行: python quick_test.py")
    
    # 询问是否立即尝试注入
    choice = input("\n是否立即尝试手动注入? (y/n): ").strip().lower()
    if choice == 'y':
        try_manual_injection()

if __name__ == "__main__":
    main()
